using System.Globalization;

namespace Payments.Abstractions;

public interface IPaymentProvider
{
    Task<CheckoutInitResult> InitializeCheckoutAsync(CheckoutInitData data, string ipAddress);
    Task<(bool IsSuccess, string? Message, string? PaymentId)> ValidateCallbackAsync(string token);
}

public sealed class CheckoutInitData
{
    public string BasketId { get; init; } = string.Empty;
    public decimal TotalPrice { get; init; }
    public Person Buyer { get; init; } = new();
    public Address Ship { get; init; } = new();
    public Address Bill { get; init; } = new();
    public List<Item> Items { get; init; } = new();
    // Callback configuration
    public string? CallbackUrl { get; init; }
    public string? CallbackOkUrl { get; init; }
    public string? CallbackFailUrl { get; init; }
}

public sealed class Person
{
    public string Id { get; init; } = string.Empty;
    public string Name { get; init; } = string.Empty;
    public string Surname { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string Phone { get; init; } = string.Empty;
    public string? IdentityNumber { get; init; }
}

public sealed class Address
{
    public string ContactName { get; init; } = string.Empty;
    public string AddressText { get; init; } = string.Empty;
    public string City { get; init; } = string.Empty;
    public string Country { get; init; } = "Turkey";
    public string? PostalCode { get; init; }
}

public sealed class Item
{
    public string Id { get; init; } = string.Empty;
    public string Name { get; init; } = string.Empty;
    public string Category1 { get; init; } = "general";
    public string ItemType { get; init; } = "PHYSICAL";
    public decimal Price { get; init; }
}

public sealed class CheckoutInitResult
{
    public bool IsSuccess { get; init; }
    public string Message { get; init; } = string.Empty;
    public string Token { get; init; } = string.Empty;
    public string? HtmlContent { get; init; }
    public string? PaymentPageUrl { get; init; }
    public object? Raw { get; init; }
}

public static class PaymentFormat
{
    public static string ToIyzicoPrice(decimal value)
        => value.ToString("0.##", CultureInfo.InvariantCulture);
}

