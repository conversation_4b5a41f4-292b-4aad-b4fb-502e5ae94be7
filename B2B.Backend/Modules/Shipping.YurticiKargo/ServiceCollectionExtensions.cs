using Microsoft.Extensions.DependencyInjection;
using Shipping.Abstraction;

namespace Shipping.YurticiKargo;

/// <summary>
/// Yurtiçi Kargo modülü için DI extension metodları
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Yurtiçi Kargo servisini DI container'a ekle
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddYurticiKargoShipping(this IServiceCollection services)
    {
        // Yurtiçi Kargo servisini transient olarak kaydet
        services.AddTransient<IShippingService, YurticiShippingService>();
        services.AddTransient<YurticiShippingService>();
        
        // Carrier definition'ı singleton olarak kaydet
        services.AddSingleton<ShippingCarrierDefinition, YurticiCarrierDefinition>();
        services.AddSingleton<YurticiCarrierDefinition>();

        return services;
    }
}
