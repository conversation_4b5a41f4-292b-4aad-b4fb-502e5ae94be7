{"providerId": "Microsoft.Tools.ServiceModel.Svcutil", "version": "2.1.0", "ExtendedData": {"inputs": ["https://testws.yurticikargo.com/KOPSWebServices/ShippingOrderDispatcherServices?wsdl"], "namespaceMappings": ["*, Shipping.YurticiKargo.KOPSWebServices"], "outputFile": "KOPSWebServicesTest.cs", "references": ["/Users/<USER>/Development/B2B/B2B.Backend/Modules/Shipping.YurticiKargo/bin/Debug/net9.0/Core.dll", "/Users/<USER>/Development/B2B/B2B.Backend/Modules/Shipping.YurticiKargo/bin/Debug/net9.0/Shipping.Abstraction.dll", "Microsoft.AspNetCore.Cryptography.Internal, {Microsoft.AspNetCore.Cryptography.Internal, 9.0.4}", "Microsoft.AspNetCore.Cryptography.KeyDerivation, {Microsoft.AspNetCore.Cryptography.KeyDerivation, 9.0.4}", "Microsoft.AspNetCore.Identity.EntityFrameworkCore, {Microsoft.AspNetCore.Identity.EntityFrameworkCore, 9.0.4}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 9.0.4}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 9.0.4}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 9.0.4}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 9.0.4}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 9.0.4}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 9.0.4}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 9.0.4}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 9.0.4}", "Microsoft.Extensions.Identity.Core, {Microsoft.Extensions.Identity.Core, 9.0.4}", "Microsoft.Extensions.Identity.Stores, {Microsoft.Extensions.Identity.Stores, 9.0.4}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 9.0.4}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 9.0.4}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 6.0.16}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 9.0.4}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 9.0.4}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.8.0}", "Microsoft.IdentityModel.Protocols.WsTrust, {Microsoft.IdentityModel.Protocols.WsTrust, 6.8.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.8.0}", "Microsoft.IdentityModel.Tokens.Saml, {Microsoft.IdentityModel.Tokens.Saml, 6.8.0}", "Microsoft.IdentityModel.Xml, {Microsoft.IdentityModel.Xml, 6.8.0}", "System.Formats.Asn1, {System.Formats.Asn1, 6.0.0}", "System.IO, {System.IO, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 6.0.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 4.5.0}", "System.Security.Cryptography.Pkcs, {System.Security.Cryptography.Pkcs, 6.0.1}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 6.0.1}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.ServiceModel.NetFramingBase, {System.ServiceModel.NetFramingBase, 6.0.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}"], "targetFramework": "net9.0", "typeReuseMode": "All"}}