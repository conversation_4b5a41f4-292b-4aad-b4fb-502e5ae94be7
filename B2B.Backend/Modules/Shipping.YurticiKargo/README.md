# Shipping.YurticiKargo Modülü

<PERSON>u modül, Yurtiçi Kargo API'si ile entegrasyon sağlayan SOLID prensiplerine uygun bir implementasyondur.

## Özellikler

- ✅ **Kargo Oluşturma**: `createShipment` API'si ile yeni kargo oluşturma
- ✅ **Kargo Sorgulama**: `queryShipment` API'si ile kargo durumu sorgulama
- ✅ **Kargo İptal**: Kargo iptal işlemleri (API dokümantasyonuna göre güncellenecek)
- ✅ **Maliyet Hesaplama**: Ağırlık ve mesafe bazlı maliyet hesaplama
- ✅ **Ayar Doğrulama**: API bağlantı ayarlarını doğrulama
- ✅ **SOAP Web Servisi**: Gerçek Yurtiçi Kargo WSDL entegrasyonu
- ✅ **Test/Canlı Ortam**: TestMode ile otomatik test/production geçişi
- ✅ **Gü<PERSON>li Kimlik Doğrulama**: Test modunda sabit kimlik bilgileri

## Teknolojiler

- .NET 9.0
- System.ServiceModel (SOAP Client)
- Microsoft.Extensions.Configuration
- Microsoft.Extensions.Logging
- Shipping.Abstraction (Base Interface)

## Kurulum

### 1. Proje Referansı

```xml
<ProjectReference Include="..\Shipping.YurticiKargo\Shipping.YurticiKargo.csproj" />
```

### 2. DI Container Kaydı

```csharp
// Startup.cs veya Program.cs
services.AddYurticiKargoShipping();
```

### 3. Ortam Değişkenleri

#### Test Modu (Önerilen)
Test modunda sabit test bilgileri kullanılır:

```bash
# Test modu aktif
YURTICI_TEST_MODE=true
# Test modunda otomatik olarak kullanılır:
# WsUserName: "YKTEST"
# WsPassword: "YK"
# WsLanguage: "TR"
```

#### Canlı Ortam
Canlı ortam için gerçek API bilgilerinizi ayarlayın:

```bash
# Canlı mod
YURTICI_TEST_MODE=false
YURTICI_WS_USERNAME=your_real_username
YURTICI_WS_PASSWORD=your_real_password
YURTICI_WS_LANGUAGE=TR
YURTICI_API_URL=https://api.yurtici.com.tr
YURTICI_TIMEOUT=30
YURTICI_MAX_RETRIES=3
```

#### appsettings.json Alternatifi

Test modu için:
```json
{
  "Shipping": {
    "Yurtici": {
      "TestMode": true,
      "ApiUrl": "https://api.yurtici.com.tr",
      "Timeout": 30,
      "MaxRetries": 3
    }
  }
}
```

Canlı ortam için:
```json
{
  "Shipping": {
    "Yurtici": {
      "WsUserName": "your_real_username",
      "WsPassword": "your_real_password",
      "WsLanguage": "TR",
      "ApiUrl": "https://api.yurtici.com.tr",
      "TestMode": false,
      "Timeout": 30,
      "MaxRetries": 3
    }
  }
}
```

## Kullanım

### Factory Pattern ile Servis Alma

```csharp
public class OrderService
{
    private readonly IShippingServiceFactory _shippingFactory;

    public OrderService(IShippingServiceFactory shippingFactory)
    {
        _shippingFactory = shippingFactory;
    }

    public async Task CreateShipment(Order order)
    {
        // Yurtiçi Kargo servisini al
        var yurticiService = _shippingFactory.GetService("YURTICI");
        
        if (yurticiService != null)
        {
            var request = new ShipmentRequest
            {
                OrderId = order.Id,
                RecipientName = order.CustomerName,
                RecipientPhone = order.Phone,
                Address = order.Address,
                City = order.City,
                District = order.District,
                PostalCode = order.PostalCode,
                Weight = order.TotalWeight
            };

            var trackingNumber = await yurticiService.CreateShipmentAsync(request);
            // Takip numarasını kaydet...
        }
    }
}
```

### Direkt Servis Kullanımı

```csharp
public class ShippingController : ControllerBase
{
    private readonly YurticiShippingService _yurticiService;

    public ShippingController(YurticiShippingService yurticiService)
    {
        _yurticiService = yurticiService;
    }

    [HttpGet("track/{trackingNumber}")]
    public async Task<IActionResult> TrackShipment(string trackingNumber)
    {
        var trackingInfo = await _yurticiService.GetTrackingInfoAsync(trackingNumber);
        return Ok(trackingInfo);
    }
}
```

## API Metodları

### CreateShipmentAsync
Yeni kargo oluşturur ve takip numarası döndürür.

### GetTrackingInfoAsync  
Takip numarası ile kargo durumunu sorgular.

### CancelShipmentAsync
Kargo iptal işlemi yapar.

### CalculateShippingCostAsync
Ağırlık ve mesafe bazlı kargo maliyeti hesaplar.

### ValidateSettingsAsync
API bağlantı ayarlarını doğrular.

## Hata Yönetimi

Tüm API çağrıları try-catch blokları ile korunmuştur ve detaylı loglama yapılmaktadır. Hata durumlarında anlamlı exception'lar fırlatılır.

## Loglama

Microsoft.Extensions.Logging kullanılarak detaylı loglama yapılmaktadır:

- Info: Başarılı işlemler
- Warning: Uyarı durumları  
- Error: Hata durumları

## Test

Modül unit test'ler için hazırdır. Mock'lanabilir interface'ler kullanılmıştır.

## Lisans

Bu modül B2B projesi kapsamında geliştirilmiştir.
