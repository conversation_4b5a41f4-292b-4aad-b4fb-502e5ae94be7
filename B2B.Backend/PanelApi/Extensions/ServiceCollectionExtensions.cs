using Mail.Abstraction;
using Mail.Implementation;
using Shipping.Abstraction;
using Shipping.Implementation;
using Shipping.YurticiKargo;

using Application.Contracts.Interfaces;
using Application.Contracts.Repositories;
using Application.Contracts.Services;
using Infrastructure.Services;
using Infrastructure.Repositories;

namespace PanelApi.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        services.AddScoped<IProductAttributeService, ProductAttributeService>();
        services.AddScoped<IProductImageService, ProductImageService>();
        services.AddScoped<IProductVolumeService, ProductVolumeService>();

        // Campaign Services
        services.AddScoped<ICampaignRepository, CampaignRepository>();
        services.AddScoped<ICampaignService, CampaignService>();
        services.AddScoped<ICampaignCalculationService, CampaignCalculationService>();

        return services;
    }

    public static IServiceCollection AddModules(this IServiceCollection services)
    {
        services.AddScoped<IMailSender, MailSender>();
        services.AddHttpClient("shipping");

        // Shipping factory pattern
        services.AddScoped<IShippingServiceFactory, ShippingServiceFactory>();

        // Shipping modules
        services.AddYurticiKargoShipping();

        services.AddScoped<IPaymentProviderRepository, PaymentProviderRepository>();
        services.AddScoped<IPaymentProviderService, PaymentProviderService>();

        return services;
    }
}