<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>PanelApi</AssemblyName>
    <RootNamespace>PanelApi</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.9.0" />
    <PackageReference Include="MassTransit" Version="8.4.0" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\Modules\Mail.Abstraction\Mail.Abstraction.csproj" />
    <ProjectReference Include="..\Modules\Mail.Implementation\Mail.Implementation.csproj" />
    <ProjectReference Include="..\Modules\Shipping.Abstraction\Shipping.Abstraction.csproj" />
    <ProjectReference Include="..\Modules\Shipping.Implementation\Shipping.Implementation.csproj" />
    <ProjectReference Include="..\Modules\Shipping.YurticiKargo\Shipping.YurticiKargo.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DTOs\" />
    <Folder Include="Services\" />
  </ItemGroup>

</Project>