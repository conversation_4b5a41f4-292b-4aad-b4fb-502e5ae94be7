"use client";
import { useContextElement } from "@/context/Context";
import Link from "next/link";
import React, { useState } from "react";
import QuantitySelect from "../common/QuantitySelect";
import { useCustomerAuth } from "@/hooks/useCustomerAuth";
import Image from "next/image";
import { formatTLPrice } from "@/utils/currency";
export default function CartModal() {
  const [openTool, setOpenTool] = useState(-1);
  const [isAgreed, setIsAgreed] = useState(false);
  const { isAuthenticated } = useCustomerAuth();
  const {
    cartProducts,
    totalPrice,
    updateQuantity,
    removeItem,
  } = useContextElement();
  return (
    <div
      className="offcanvas offcanvas-end popup-style-1 popup-shopping-cart"
      id="shoppingCart"
    >
      <div className="canvas-wrapper">
        <div className="popup-header">
          <span className="title">Sepetiniz</span>
          <span
            className="icon-close icon-close-popup"
            data-bs-dismiss="offcanvas"
          />
        </div>
        <div className="wrap">
          {/* {totalPrice < shippingFreeLimit ? */}
          {/*   (<div className="tf-mini-cart-threshold"> */}
          {/*     <div className="text"> */}
          {/*       <span className="fw-medium">Ücretsiz Kargo</span>'dan yararlanmak için <span className="fw-medium">{formatTLPrice(shippingFreeLimit)}</span> lik ürün ekleyiniz. */}
          {/*     </div> */}
          {/*     <div className="tf-progress-bar tf-progress-ship"> */}
          {/*       <ProgressBarComponent current={totalPrice} max={shippingFreeLimit}> */}
          {/*         <i className="icon icon-car" /> */}
          {/*       </ProgressBarComponent> */}
          {/*     </div> */}
          {/*   </div>) : */}
          {/*   ( */}
          {/*     <div className="tf-mini-cart-threshold"> */}
          {/*       <div className="text"> */}
          {/*         <span className="fw-medium">Kargo Ücretsiz!</span> 🎉 */}
          {/*       </div> */}
          {/*       <div className="tf-progress-bar tf-progress-ship"> */}
          {/*         <ProgressBarComponent current={shippingFreeLimit} max={shippingFreeLimit}> */}
          {/*           <i className="icon icon-car" /> */}
          {/*         </ProgressBarComponent> */}
          {/*       </div> */}
          {/*     </div> */}
          {/*   ) */}
          {/* } */}
          <div className="tf-mini-cart-wrap">
            <div className="tf-mini-cart-main">
              <div className="tf-mini-cart-sroll">
                {cartProducts.length ? (
                  <div className="tf-mini-cart-items">
                    {cartProducts.map((product, i) => (
                      <div key={i} className="tf-mini-cart-item file-delete">
                        <div className="tf-mini-cart-image">
                          <Link href={`/urun-detay/${product.id}`}>
                            <Image
                              className="lazyload"
                              alt="img-product"
                              src={product.image}
                              width={190}
                              height={252}
                            />
                          </Link>
                        </div>
                        <div className="tf-mini-cart-info">
                          <div className="d-flex justify-content-between">
                            <Link
                              className="title link text-md fw-medium"
                              href={`/urun-detay/${product.id}`}
                            >
                              {product.name}
                            </Link>
                            <i
                              className="icon icon-close remove fs-12"
                              onClick={() => removeItem(product.id)}
                            />
                          </div>
                          {/* TODO: Varyant eklendiğinde burası açılacak */}
                          {/* <div className="d-flex gap-10"> */}
                          {/*   <div className="text-xs">White / L</div> */}
                          {/*   <a href="#" className="link edit"> */}
                          {/*     <i className="icon-pen" /> */}
                          {/*   </a> */}
                          {/* </div> */}
                          <p className="price-wrap text-sm fw-medium">
                            <span className="new-price text-primary">
                              {formatTLPrice(product.price * product.quantity)}
                            </span>{" "}
                            {product.oldPrice && (
                              <span className="old-price text-decoration-line-through text-dark-1">
                                {formatTLPrice(product.oldPrice * product.quantity)}
                              </span>
                            )}
                          </p>
                          <QuantitySelect
                            styleClass="small"
                            quantity={product.quantity}
                            setQuantity={(qty) => {
                              updateQuantity(product.id, qty);
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-4">
                    Sepetiniz boş. Sepetinize ürün eklediğinizde burada gözükecektir.{" "}
                    <Link
                      className="tf-btn btn-dark2 animate-btn mt-3"
                      href="/urunler"
                    >
                      Ürünlerimizi Keşfedin
                    </Link>
                  </div>
                )}
                {/* <div className="tf-minicart-recommendations"> */}
                {/*   <div className="tf-minicart-recommendations-heading d-flex justify-content-between align-items-end"> */}
                {/*     <div className="tf-minicart-recommendations-title text-md fw-medium"> */}
                {/*       En Çok Satan Ürünlerimiz */}
                {/*     </div> */}
                {/*     <div className="d-flex gap-10"> */}
                {/*       <div className="swiper-button-prev nav-swiper arrow-1 size-30 nav-prev-cls" /> */}
                {/*       <div className="swiper-button-next nav-swiper arrow-1 size-30 nav-next-cls" /> */}
                {/*     </div> */}
                {/*   </div> */}
                {/*   <Swiper */}
                {/*     dir="ltr" */}
                {/*     className="swiper tf-swiper" */}
                {/*     {...{ */}
                {/*       slidesPerView: 1, */}
                {/*       spaceBetween: 10, */}
                {/*       speed: 800, */}
                {/*       autoplay: "play", */}
                {/*       observer: true, */}
                {/*       observeParents: true, */}
                {/*       slidesPerGroup: 1, */}
                {/*       navigation: { */}
                {/*         clickable: true, */}
                {/*         nextEl: ".nav-next-cls", */}
                {/*         prevEl: ".nav-prev-cls", */}
                {/*       }, */}
                {/*     }} */}
                {/*     modules={[Navigation]} */}
                {/*   > */}
                {/*     {products1.slice(0, 4).map((product, i) => ( */}
                {/*       <SwiperSlide key={i} className="swiper-slide"> */}
                {/*         <div className="tf-mini-cart-item line radius-16"> */}
                {/*           <div className="tf-mini-cart-image"> */}
                {/*             <Link href={`/urun-detay/${product.id}`}> */}
                {/*               <Image */}
                {/*                 className="lazyload" */}
                {/*                 alt="img-product" */}
                {/*                 src={product.imgSrc} */}
                {/*                 width={684} */}
                {/*                 height={972} */}
                {/*               /> */}
                {/*             </Link> */}
                {/*           </div> */}
                {/*           <div className="tf-mini-cart-info justify-content-center"> */}
                {/*             <Link */}
                {/*               className="title link text-md fw-medium" */}
                {/*               href={`/urun-detay/${product.id}`} */}
                {/*             > */}
                {/*               {product.title} */}
                {/*             </Link> */}
                {/*             <p className="price-wrap text-sm fw-medium"> */}
                {/*               <span className="new-price text-primary"> */}
                {/*                 {formatTLPrice(product.price)} */}
                {/*               </span>{" "} */}
                {/*               {product.oldPrice && ( */}
                {/*                   <span className="old-price text-decoration-line-through text-dark-1"> */}
                {/*                     {formatTLPrice(product.oldPrice)} */}
                {/*                   </span> */}
                {/*                 )} */}
                {/*               </p> */}
                {/*               <a */}
                {/*                 className="tf-btn animate-btn d-inline-flex bg-dark-2 w-max-content" */}
                {/*                 onClick={() => addProductToCart(product.id)} */}
                {/*               > */}
                {/*                 {isAddedToCartProducts(product.id) */}
                {/*                   ? "Sepette Mevcut" */}
                {/*                   : "Sepete Ekle"} */}
                {/*               </a> */}
                {/*             </div> */}
                {/*           </div> */}
                {/*         </SwiperSlide> */}
                {/*       ))} */}
                {/*     </Swiper> */}
                {/*   </div> */}
              </div>
            </div>
            <div className="tf-mini-cart-bottom">
              {/* <div className="tf-mini-cart-tool"> */}
              {/*   <div */}
              {/*     className="tf-mini-cart-tool-btn btn-add-gift" */}
              {/*     onClick={() => setOpenTool((pre) => (pre == 1 ? -1 : 1))} */}
              {/*   > */}
              {/*     <i className="icon icon-gift2" /> */}
              {/*     <div className="text-xxs">Add gift wrap</div> */}
              {/*   </div> */}
              {/*   <div */}
              {/*     className="tf-mini-cart-tool-btn btn-add-note" */}
              {/*     onClick={() => setOpenTool((pre) => (pre == 2 ? -1 : 2))} */}
              {/*   > */}
              {/*     <i className="icon icon-note" /> */}
              {/*     <div className="text-xxs">Order note</div> */}
              {/*   </div> */}
              {/*   <div */}
              {/*     className="tf-mini-cart-tool-btn btn-coupon" */}
              {/*     onClick={() => setOpenTool((pre) => (pre == 3 ? -1 : 3))} */}
              {/*   > */}
              {/*     <i className="icon icon-coupon" /> */}
              {/*     <div className="text-xxs">Coupon</div> */}
              {/*   </div> */}
              {/*   <div */}
              {/*     className="tf-mini-cart-tool-btn btn-estimate-shipping" */}
              {/*     onClick={() => setOpenTool((pre) => (pre == 4 ? -1 : 4))} */}
              {/*   > */}
              {/*     <i className="icon icon-car" /> */}
              {/*     <div className="text-xxs">Shipping</div> */}
              {/*   </div> */}
              {/* </div> */}
              <div className="tf-mini-cart-bottom-wrap">
                <div className="tf-cart-totals-discounts">
                  <div className="tf-cart-total text-xl fw-medium">Sepet Toplamı:</div>
                  <div className="tf-totals-total-value text-xl fw-medium">
                    {formatTLPrice(totalPrice)}
                  </div>
                </div>
                <div className="tf-cart-tax text-sm opacity-8">
                  Vergiler fiyata dahildir. <br />
                  Kargo Ücretsizdir.
                </div>
                <div className="tf-cart-checkbox">
                  <div className="tf-checkbox-wrapp">
                    <input
                      className=""
                      type="checkbox"
                      id="CartDrawer-Form_agree"
                      name="agree_checkbox"
                      checked={isAgreed}
                      onChange={(e) => setIsAgreed(e.target.checked)}
                    />
                    <div>
                      <i className="icon-check" />
                    </div>
                  </div>
                  <label htmlFor="CartDrawer-Form_agree" className="text-sm">
                    <Link
                      href={`/mesafeli-satis-sozlesmesi`}
                      title="Mesafeli Satış Sözleşmesi"
                      className="fw-medium text-decoration-underline"
                    >
                      Mesafeli Satış Sözleşmesi
                    </Link>
                    'ni kabul ediyorum.
                  </label>
                </div>
                <div className="tf-mini-cart-view-checkout">
                  <Link
                    href={`/sepetim`}
                    className="tf-btn animate-btn d-inline-flex bg-dark-2 w-100 justify-content-center"
                  >
                    Sepet'e git
                  </Link>
                  <Link
                    href={`/satin-al`}
                    className="tf-btn btn-out-line-dark2 w-100 justify-content-center"
                    onClick={(e) => {
                      if (!isAgreed) {
                        e.preventDefault();
                        alert("Devam etmek için 'Mesafeli Satış Sözleşmesi'ni' onaylamalısınız.");
                        return;
                      }
                      if (!isAuthenticated) {
                        e.preventDefault();
                        alert("Devam etmek için lütfen giriş yapın veya kayıt olun.");
                        (async () => {
                          try {
                            const loginModal = document.getElementById('login');
                            if (loginModal) {
                              const bootstrap = await import('bootstrap');
                              const instance = new bootstrap.Offcanvas(loginModal);
                              instance.show();
                            }
                          } catch { }
                        })();
                      }
                    }}
                  >
                    <span>Satın Al</span>
                  </Link>
                </div>
              </div>
            </div>
            {/* <div */}
            {/*   className={`tf-mini-cart-tool-openable add-gift ${openTool == 1 ? "open" : "" */}
            {/*     }`} */}
            {/* > */}
            {/*   <div */}
            {/*     className="overplay tf-mini-cart-tool-close" */}
            {/*     onClick={() => setOpenTool(-1)} */}
            {/*   /> */}
            {/*   <form action="#" className="tf-mini-cart-tool-content"> */}
            {/*     <div className="tf-mini-cart-tool-text text-sm fw-medium"> */}
            {/*       Add gift wrap */}
            {/*     </div> */}
            {/*     <div className="tf-mini-cart-tool-text1 text-dark-1"> */}
            {/*       The product will be wrapped carefully. Free is only */}
            {/*       <span className="text fw-medium text-dark">{formatTLPrice(10)}</span>. Do */}
            {/*       you want a gift wrap? */}
            {/*     </div> */}
            {/*     <div className="tf-cart-tool-btns"> */}
            {/*       <button */}
            {/*         className="subscribe-button tf-btn animate-btn d-inline-flex bg-dark-2 w-100" */}
            {/*         type="submit" */}
            {/*       > */}
            {/*         Save */}
            {/*       </button> */}
            {/*       <div */}
            {/*         className="tf-btn btn-out-line-dark2 w-100 tf-mini-cart-tool-close" */}
            {/*         onClick={() => setOpenTool(-1)} */}
            {/*       > */}
            {/*         Close */}
            {/*       </div> */}
            {/*     </div> */}
            {/*   </form> */}
            {/* </div> */}
            {/* <div */}
            {/*   className={`tf-mini-cart-tool-openable add-note  ${openTool == 2 ? "open" : "" */}
            {/*     }`} */}
            {/* > */}
            {/*   <div */}
            {/*     className="overplay tf-mini-cart-tool-close" */}
            {/*     onClick={() => setOpenTool(-1)} */}
            {/*   /> */}
            {/*   <form action="#" className="tf-mini-cart-tool-content"> */}
            {/*     <label */}
            {/*       htmlFor="Cart-note" */}
            {/*       className="tf-mini-cart-tool-text text-sm fw-medium" */}
            {/*     > */}
            {/*       Order note */}
            {/*     </label> */}
            {/*     <textarea */}
            {/*       name="note" */}
            {/*       id="Cart-note" */}
            {/*       placeholder="Instruction for seller..." */}
            {/*       defaultValue={""} */}
            {/*     /> */}
            {/*     <div className="tf-cart-tool-btns"> */}
            {/*       <button */}
            {/*         className="subscribe-button tf-btn animate-btn d-inline-flex bg-dark-2 w-100" */}
            {/*         type="submit" */}
            {/*       > */}
            {/*         Save */}
            {/*       </button> */}
            {/*       <div */}
            {/*         className="tf-btn btn-out-line-dark2 w-100 tf-mini-cart-tool-close" */}
            {/*         onClick={() => setOpenTool(-1)} */}
            {/*       > */}
            {/*         Close */}
            {/*       </div> */}
            {/*     </div> */}
            {/*   </form> */}
            {/* </div> */}
            <div
              className={`tf-mini-cart-tool-openable coupon  ${openTool == 3 ? "open" : ""
                }`}
            >
              <div
                className="overplay tf-mini-cart-tool-close"
                onClick={() => setOpenTool(-1)}
              />
              <form action="#" className="tf-mini-cart-tool-content">
                <div className="tf-mini-cart-tool-text text-sm fw-medium">
                  Add coupon
                </div>
                <div className="tf-mini-cart-tool-text1 text-dark-1">
                  * Discount will be calculated and applied at checkout
                </div>
                <input type="text" name="text" placeholder="" />
                <div className="tf-cart-tool-btns">
                  <button
                    className="subscribe-button tf-btn animate-btn d-inline-flex bg-dark-2 w-100"
                    type="submit"
                  >
                    Add a Gift Wrap
                  </button>
                  <div
                    className="tf-btn btn-out-line-dark2 w-100 tf-mini-cart-tool-close"
                    onClick={() => setOpenTool(-1)}
                  >
                    Cancel
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
