"use client";
import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { accountRequests } from '@/services/account.js';

// Varsayılan boş adres formu state'i
const initialAddressState = {
  name: "",
  line1: "",
  line2: "",
  city: "",
  district: "",
  country: "Türkiye",
  postalCode: "",
  addressType: 1, // Shipping=1 (teslimat adresi)
  isDefault: false,
};

export default function CheckoutAddressSelector({
  customerId,
  selectedAddress,
  onAddressSelect,
  addressType = 1, // 0=Billing, 1=Shipping
  title = "Teslimat Adresi"
}) {
  const [addresses, setAddresses] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState(null);
  const [newAddress, setNewAddress] = useState({ ...initialAddressState, addressType });
  const [loading, setLoading] = useState(true);

  // Adresleri API'den çekmek için
  const fetchAddresses = async () => {
    if (!customerId) return;
    try {
      setLoading(true);
      const response = await accountRequests.getAddresses(customerId);
      // Sadece belirtilen adres tipindeki adresleri filtrele
      const filteredAddresses = response?.filter(addr => addr.addressType === addressType) || [];
      setAddresses(filteredAddresses);

      // Eğer seçili adres yoksa ve varsayılan adres varsa onu seç
      if (!selectedAddress && filteredAddresses.length > 0) {
        const defaultAddress = filteredAddresses.find(addr => addr.isDefault) || filteredAddresses[0];
        onAddressSelect(defaultAddress);
      }
    } catch (error) {
      console.error("Adresler getirilirken hata oluştu:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAddresses();
  }, [customerId, addressType]);

  // Form input değişikliklerini yönet
  const handleInputChange = (e) => {
    const { id, value, type, checked } = e.target;
    let finalValue = value;

    if (type === "checkbox") {
      finalValue = checked;
    }

    setNewAddress((prevAddress) => ({
      ...prevAddress,
      [id]: finalValue,
    }));
  };

  // Yeni Adres Ekleme
  const handleAddAddress = async (e) => {
    e.preventDefault();
    try {
      const payload = { ...newAddress, customerId, addressType };
      await accountRequests.addAddress(payload);
      await fetchAddresses();
      setShowAddForm(false);
      setNewAddress({ ...initialAddressState, addressType });
    } catch (error) {
      console.error("Adres eklenirken bir hata oluştu:", error);
    }
  };

  // Adres Düzenleme
  const handleEditAddress = (id) => {
    const addressToEdit = addresses.find((addr) => addr.id === id);
    if (addressToEdit) {
      setNewAddress({
        name: addressToEdit.name,
        line1: addressToEdit.line1,
        line2: addressToEdit.line2 || "",
        city: addressToEdit.city,
        district: addressToEdit.district,
        country: addressToEdit.country || "Türkiye",
        postalCode: addressToEdit.postalCode || "",
        addressType: addressToEdit.addressType,
        isDefault: addressToEdit.isDefault,
      });
      setEditingAddressId(id);
      setShowAddForm(false);
    }
  };

  // Adres Güncelleme
  const handleUpdateAddress = async (e) => {
    e.preventDefault();
    try {
      const payload = { ...newAddress, id: editingAddressId };
      await accountRequests.updateAddress(payload);
      await fetchAddresses();
      setEditingAddressId(null);
      setNewAddress({ ...initialAddressState, addressType });
    } catch (error) {
      console.error("Adres güncellenirken bir hata oluştu:", error);
    }
  };

  // Adres Silme
  const handleDeleteAddress = async (id) => {
    if (window.confirm("Bu adresi silmek istediğinizden emin misiniz?")) {
      try {
        await accountRequests.removeAddress(id);
        await fetchAddresses();
        // Eğer silinen adres seçili adres ise, seçimi temizle
        if (selectedAddress?.id === id) {
          onAddressSelect(null);
        }
      } catch (error) {
        console.error("Adres silinirken bir hata oluştu:", error);
      }
    }
  };

  // Form iptal
  const handleCancel = () => {
    setShowAddForm(false);
    setEditingAddressId(null);
    setNewAddress({ ...initialAddressState, addressType });
  };

  // Modal aç/kapat
  const toggleModal = () => {
    setShowModal(!showModal);
    if (!showModal) {
      // Modal açılırken formu temizle
      handleCancel();
    }
  };

  // Adres seçimi
  const handleSelectAddress = (address) => {
    onAddressSelect(address);
    setShowModal(false);
  };

  const renderAddressForm = (handleSubmit, buttonText) => (
    <form onSubmit={handleSubmit} className="wd-form-address form-default" style={{ marginTop: "20px" }}>
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="name">Adres Adı</label>
        <input type="text" id="name" value={newAddress.name} onChange={handleInputChange} required />
      </fieldset>
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="line1">Adres Satırı 1</label>
        <input type="text" id="line1" value={newAddress.line1} onChange={handleInputChange} required />
      </fieldset>
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="line2">Adres Satırı 2 (İsteğe Bağlı)</label>
        <input type="text" id="line2" value={newAddress.line2} onChange={handleInputChange} />
      </fieldset>
      <div className="cols-2">
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="district">İlçe</label>
          <input type="text" id="district" value={newAddress.district} onChange={handleInputChange} required />
        </fieldset>
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="city">İl (Şehir)</label>
          <input type="text" id="city" value={newAddress.city} onChange={handleInputChange} required />
        </fieldset>
      </div>
      <div className="cols-2">
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="country">Ülke</label>
          <input type="text" id="country" value={newAddress.country} onChange={handleInputChange} required />
        </fieldset>
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="postalCode">Posta Kodu</label>
          <input type="text" id="postalCode" value={newAddress.postalCode} onChange={handleInputChange} />
        </fieldset>
      </div>
      <div className="tf-cart-checkbox" style={{ marginTop: "16px" }}>
        <input type="checkbox" className="tf-check" id="isDefault" checked={newAddress.isDefault} onChange={handleInputChange} />
        <label htmlFor="isDefault"><span>Varsayılan adres olarak ayarla</span></label>
      </div>
      <div className="box-btn" style={{ marginTop: "24px" }}>
        <button className="tf-btn animate-btn" type="submit">{buttonText}</button>
        <button type="button" className="tf-btn btn-out-line-dark" onClick={handleCancel}>İptal</button>
      </div>
    </form>
  );

  return (
    <>
      {/* Adres Seçim Kutusu */}
      <div className="box-ip-checkout">
        <div className="title text-xl fw-medium">{title}</div>
        {selectedAddress ? (
          <div className="selected-address-box" style={{ border: "1px solid #ddd", padding: "16px", borderRadius: "8px", marginBottom: "16px" }}>
            <div className="d-flex justify-content-between align-items-start">
              <div>
                <p className="fw-medium">{selectedAddress.name}</p>
                <p className="text-sm">{selectedAddress.line1} {selectedAddress.line2}</p>
                <p className="text-sm">{selectedAddress.district} / {selectedAddress.city}</p>
                <p className="text-sm">{selectedAddress.country} {selectedAddress.postalCode}</p>
              </div>
              <button
                type="button"
                className="tf-btn btn-out-line-dark btn-sm"
                onClick={toggleModal}
              >
                Değiştir
              </button>
            </div>
          </div>
        ) : (
          <div className="no-address-box" style={{ border: "1px dashed #ddd", padding: "16px", borderRadius: "8px", marginBottom: "16px", textAlign: "center" }}>
            <p className="text-sm text-muted">Henüz adres seçilmedi</p>
            <button
              type="button"
              className="tf-btn animate-btn"
              onClick={toggleModal}
            >
              Adres Seç
            </button>
          </div>
        )}
      </div>

      {/* Modal - Portal ile render et */}
      {showModal && typeof document !== 'undefined' && createPortal(
        <div
          className="modal fade show"
          style={{ display: "block", backgroundColor: "rgba(0,0,0,0.5)", position: "fixed", top: 0, left: 0, width: "100%", height: "100%", zIndex: 1050 }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              toggleModal();
            }
          }}
        >
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">{title} Seçin</h5>
                <button type="button" className="btn-close" onClick={toggleModal}></button>
              </div>
              <div className="modal-body">
                {!showAddForm && editingAddressId === null && (
                  <button
                    className="tf-btn btn-add-address animate-btn mb-3"
                    onClick={() => { setShowAddForm(true); setEditingAddressId(null); }}
                  >
                    Yeni adres ekle
                  </button>
                )}

                {showAddForm && renderAddressForm(handleAddAddress, "Adres Ekle")}
                {editingAddressId !== null && renderAddressForm(handleUpdateAddress, "Güncelle")}

                {loading ? (
                  <p>Adresler yükleniyor...</p>
                ) : (
                  <div className="address-list">
                    {addresses.length ? addresses.map((address) => (
                      <div key={address.id} className="address-item" style={{ border: "1px solid #ddd", padding: "16px", borderRadius: "8px", marginBottom: "12px" }}>
                        <div className="d-flex justify-content-between">
                          <div className="flex-grow-1" onClick={() => handleSelectAddress(address)} style={{ cursor: "pointer" }}>
                            <p className="fw-medium">
                              {address.name} {address.isDefault && "(Varsayılan)"}
                            </p>
                            <p className="text-sm">{address.line1} {address.line2}</p>
                            <p className="text-sm">{address.district} / {address.city}</p>
                          </div>
                          <div className="d-flex gap-2">
                            <button
                              className="tf-btn btn-out-line-dark btn-sm"
                              onClick={() => handleEditAddress(address.id)}
                            >
                              Düzenle
                            </button>
                            <button
                              className="tf-btn btn-out-line-dark btn-sm"
                              onClick={() => handleDeleteAddress(address.id)}
                            >
                              Sil
                            </button>
                          </div>
                        </div>
                      </div>
                    )) : (
                      <p>Kayıtlı adresiniz bulunmamaktadır.</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}
