export const openCartModal = async () => {
  try {
    // Dinamik olarak bootstrap'i import et
    const bootstrap = await import('bootstrap');

    // Açık olan modal'ları kapat
    const modalElements = document.querySelectorAll(".modal.show");
    modalElements.forEach((modal) => {
      const modalInstance = bootstrap.Modal.getInstance(modal);
      if (modalInstance) {
        modalInstance.hide();
      }
    });

    // Açık olan offcanvas'ları kapat
    const offcanvasElements = document.querySelectorAll(".offcanvas.show");
    offcanvasElements.forEach((offcanvas) => {
      const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvas);
      if (offcanvasInstance) {
        offcanvasInstance.hide();
      }
    });

    // Sepet modalını aç
    const cartElement = document.getElementById("shoppingCart");
    if (cartElement) {
      const cartOffcanvas = new bootstrap.Offcanvas(cartElement);
      cartOffcanvas.show();
    }
  } catch (error) {
    console.error('Bootstrap import hatası:', error);
  }
};
